<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- 横屏布局：左右分栏 -->
    
    <!-- 左侧：标题和配置选择 -->
    <LinearLayout
        android:id="@+id/leftPanel"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingEnd="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rightPanel"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.5">

        <TextView
            android:id="@+id/textViewAppTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择配置文件:"
            android:textStyle="bold"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"/>

        <Spinner
            android:id="@+id/spinnerConfigFiles"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"/>

        <TextView
            android:id="@+id/textViewCurrentConfig"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="当前配置: 默认内置系数"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="24dp"/>

    </LinearLayout>

    <!-- 右侧：模式选择按钮 -->
    <LinearLayout
        android:id="@+id/rightPanel"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingStart="8dp"
        android:gravity="center"
        app:layout_constraintStart_toEndOf="@+id/leftPanel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.5">

        <Button
            android:id="@+id/buttonMode2D"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2D模式配置与预览"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            android:minHeight="60dp"/>

        <Button
            android:id="@+id/buttonMode3D"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="3D模式配置与预览"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"
            android:minHeight="60dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="提示：请先连接AR眼镜作为副屏"
            android:textSize="12sp"
            android:textColor="@android:color/holo_orange_dark"
            android:gravity="center"
            android:layout_marginTop="24dp"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
