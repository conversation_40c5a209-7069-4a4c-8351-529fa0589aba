/// @ref gtx_scalar_relational
/// @file glm/gtx/scalar_relational.hpp
///
/// @see core (dependence)
///
/// @defgroup gtx_scalar_relational GLM_GTX_scalar_relational
/// @ingroup gtx
///
/// Include <glm/gtx/scalar_relational.hpp> to use the features of this extension.
///
/// Extend a position from a source to a position at a defined length.

#pragma once

// Dependency:
#include "../glm.hpp"

#if GLM_MESSAGES == GLM_ENABLE && !defined(GLM_EXT_INCLUDED)
#	ifndef GLM_ENABLE_EXPERIMENTAL
#		pragma message("GLM: GLM_GTX_extend is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.")
#	else
#		pragma message("GLM: GLM_GTX_extend extension included")
#	endif
#endif

namespace glm
{
	/// @addtogroup gtx_scalar_relational
	/// @{



	/// @}
}//namespace glm

#include "scalar_relational.inl"
