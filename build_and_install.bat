@echo off
echo Building and installing AR Waveguide Demo...

REM Check if ADB is available
adb version
if %ERRORLEVEL% neq 0 (
    echo ADB not found. Please make sure Android SDK is installed and ADB is in your PATH.
    exit /b 1
)

REM Check if device is connected
adb devices
if %ERRORLEVEL% neq 0 (
    echo No device connected. Please connect an Android device.
    exit /b 1
)

REM Install the APK (assuming it's already built)
echo Installing APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo Failed to install APK. Make sure the APK exists.
    exit /b 1
)

REM Launch the app
echo Launching app...
adb shell am start -n com.candee.osttest/.MainActivity

echo Done!
