<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:padding="8dp"
    tools:context=".Mode2DActivity">

    <!-- 左侧面板：配置和控制 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.4"
        android:paddingEnd="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Configuration Selection Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="选择配置文件:"
                android:textStyle="bold"
                android:textSize="12sp"
                android:layout_marginTop="8dp"/>

            <Spinner
                android:id="@+id/spinnerConfigFiles"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"/>

            <!-- FOV Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="视场角 (FOV):"
                android:textStyle="bold"
                android:textSize="12sp"
                android:layout_marginTop="16dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <com.google.android.material.slider.Slider
                    android:id="@+id/sliderFov"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:valueFrom="20"
                    android:valueTo="60"
                    android:value="40"
                    android:stepSize="1"/>

                <TextView
                    android:id="@+id/textViewFovValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="40°"
                    android:textSize="12sp"
                    android:minWidth="40dp"
                    android:gravity="center"/>

            </LinearLayout>

            <!-- Aspect Ratio Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="宽高比:"
                android:textStyle="bold"
                android:textSize="12sp"
                android:layout_marginTop="16dp"/>

            <Spinner
                android:id="@+id/spinnerAspectRatio2d"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"/>

            <!-- Image Selection Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="图片选择:"
                android:textStyle="bold"
                android:textSize="12sp"
                android:layout_marginTop="16dp"/>

            <Button
                android:id="@+id/buttonSelectImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="选择本地图片"
                android:textSize="12sp"
                android:layout_marginTop="4dp"/>

            <TextView
                android:id="@+id/textViewSelectedImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="未选择图片"
                android:textSize="10sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp"/>

            <!-- Control Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp">

                <Button
                    android:id="@+id/buttonApplyToSecondary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="应用到副屏"
                    android:textSize="12sp"
                    android:layout_marginBottom="8dp"/>

                <Button
                    android:id="@+id/buttonStopSecondary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="停止副屏显示"
                    android:textSize="12sp"
                    android:layout_marginBottom="8dp"/>

                <Button
                    android:id="@+id/buttonSaveDistortedImage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保存反畸变图像"
                    android:textSize="12sp"/>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <!-- 右侧面板：畸变系数编辑 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.6"
        android:orientation="vertical"
        android:paddingStart="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="畸变系数编辑:"
            android:textStyle="bold"
            android:textSize="12sp"
            android:layout_marginBottom="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <!-- DX Coefficients -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingEnd="4dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="DX系数 (21个):"
                    android:textStyle="bold"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp"/>

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <GridLayout
                        android:id="@+id/gridLayoutDxCoeffs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:columnCount="3"
                        android:orientation="horizontal"/>

                </ScrollView>

            </LinearLayout>

            <!-- DY Coefficients -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingStart="4dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="DY系数 (21个):"
                    android:textStyle="bold"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp"/>

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <GridLayout
                        android:id="@+id/gridLayoutDyCoeffs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:columnCount="3"
                        android:orientation="horizontal"/>

                </ScrollView>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
