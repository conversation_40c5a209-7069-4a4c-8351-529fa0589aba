package com.candee.osttest.utils;

import android.content.Context;
import android.text.InputType;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.TextView;
import java.util.ArrayList;
import java.util.List;

/**
 * 21项式系数编辑辅助类
 */
public class CoefficientEditHelper {

    public static EditText[] createCoefficientEditTexts(Context context, GridLayout gridLayout, String prefix) {
        EditText[] editTexts = new EditText[21];
        gridLayout.removeAllViews();

        // 设置GridLayout为6列，每行3对（标签+编辑框）
        gridLayout.setColumnCount(6);

        for (int i = 0; i < 21; i++) {
            TextView label = new TextView(context);
            label.setText(prefix + "[" + i + "]:");
            label.setTextSize(10);
            label.setPadding(4, 4, 4, 4);

            EditText editText = new EditText(context);
            editText.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL | InputType.TYPE_NUMBER_FLAG_SIGNED);
            editText.setTextSize(10);
            editText.setPadding(4, 4, 4, 4);
            editText.setMinWidth(150);

            editTexts[i] = editText;

            // 计算行和列位置
            int row = i / 3;  // 每行3对
            int pairInRow = i % 3;  // 在行中的第几对
            int labelCol = pairInRow * 2;  // 标签列
            int editCol = pairInRow * 2 + 1;  // 编辑框列

            GridLayout.LayoutParams labelParams = new GridLayout.LayoutParams();
            labelParams.columnSpec = GridLayout.spec(labelCol);
            labelParams.rowSpec = GridLayout.spec(row);
            gridLayout.addView(label, labelParams);

            GridLayout.LayoutParams editParams = new GridLayout.LayoutParams();
            editParams.columnSpec = GridLayout.spec(editCol);
            editParams.rowSpec = GridLayout.spec(row);
            gridLayout.addView(editText, editParams);
        }

        return editTexts;
    }

    public static void loadCoefficientsToEditTexts(EditText[] editTexts, List<Double> coefficients) {
        for (int i = 0; i < 21 && i < coefficients.size(); i++) {
            editTexts[i].setText(String.format("%.12e", coefficients.get(i)));
        }
    }

    public static List<Double> getCoefficientsFromEditTexts(EditText[] editTexts) throws NumberFormatException {
        List<Double> coefficients = new ArrayList<>();
        for (int i = 0; i < 21; i++) {
            String text = editTexts[i].getText().toString();
            coefficients.add(Double.parseDouble(text));
        }
        return coefficients;
    }
}
