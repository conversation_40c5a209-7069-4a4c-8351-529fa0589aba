package com.candee.osttest;

import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import com.candee.osttest.databinding.ActivityMainBinding;
import com.candee.osttest.utils.Constants;
import com.candee.osttest.utils.DistortionParams;
import com.candee.osttest.utils.FileUtils;
import com.candee.osttest.utils.ConfigLoader;
import com.candee.osttest.utils.GlobalConfig;
import java.util.ArrayList;
import java.util.List;
import android.util.Log;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";

    private ActivityMainBinding binding;

    private List<ConfigLoader.ConfigInfo> availableConfigs;
    private ArrayAdapter<String> configAdapter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setTitle(R.string.app_name);

        // 初始化全局配置为默认配置
        GlobalConfig.getInstance().initializeWithDefaultConfig(this);

        // 初始化配置文件选择器
        initConfigSpinner();

        binding.buttonMode2D.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, Mode2DActivity.class);
            startActivity(intent);
        });

        binding.buttonMode3D.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, Mode3DActivity.class);
            startActivity(intent);
        });



        updateCurrentConfigDisplay();
    }

    private void initConfigSpinner() {
        // 获取所有可用的配置文件（包括预设和用户保存的）
        availableConfigs = getAllAvailableConfigs();

        // 创建显示名称列表
        String[] displayNames = new String[availableConfigs.size()];
        for (int i = 0; i < availableConfigs.size(); i++) {
            displayNames[i] = availableConfigs.get(i).displayName;
        }

        // 设置适配器
        configAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, displayNames);
        configAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerConfigFiles.setAdapter(configAdapter);

        // 设置选择监听器
        binding.spinnerConfigFiles.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position >= 0 && position < availableConfigs.size()) {
                    ConfigLoader.ConfigInfo selectedConfig = availableConfigs.get(position);
                    loadSelectedConfig(selectedConfig);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Do nothing
            }
        });

        // 默认选择第一个配置（设计文件版本）
        if (!availableConfigs.isEmpty()) {
            binding.spinnerConfigFiles.setSelection(0);
            loadSelectedConfig(availableConfigs.get(0));
        }
    }

    /**
     * 获取所有可用的配置文件（包括预设和用户保存的）
     */
    private List<ConfigLoader.ConfigInfo> getAllAvailableConfigs() {
        List<ConfigLoader.ConfigInfo> allConfigs = new ArrayList<>();

        // 添加预设配置文件
        allConfigs.addAll(ConfigLoader.getAvailableConfigs());

        // 添加用户保存的配置文件
        String[] savedFiles = FileUtils.getSavedConfigFiles(this);
        if (savedFiles != null) {
            for (String savedFile : savedFiles) {
                // 为用户保存的文件创建ConfigInfo，使用特殊标记表示这是用户文件
                allConfigs.add(new ConfigLoader.ConfigInfo(
                    "USER:" + savedFile,  // 使用特殊前缀标记用户文件
                    savedFile + " (用户保存)",
                    "用户保存的配置文件"
                ));
            }
        }

        return allConfigs;
    }

    private void loadSelectedConfig(ConfigLoader.ConfigInfo configInfo) {
        Log.i(TAG, "loadSelectedConfig: Loading config " + configInfo.displayName + " (fileName: " + configInfo.fileName + ")");

        // 更新描述文本
        binding.textViewConfigDescription.setText(configInfo.description);

        DistortionParams params = null;

        // 检查是否是用户保存的配置文件
        if (configInfo.fileName.startsWith("USER:")) {
            // 用户保存的配置文件
            String actualFileName = configInfo.fileName.substring(5); // 移除"USER:"前缀
            Log.i(TAG, "loadSelectedConfig: Loading user saved config: " + actualFileName);
            params = FileUtils.loadDistortionParams(this, actualFileName);
        } else {
            // 预设配置文件
            Log.i(TAG, "loadSelectedConfig: Loading preset config from assets: " + configInfo.fileName);
            params = ConfigLoader.loadConfigFromAssets(this, configInfo.fileName);
        }

        if (params != null) {
            // 强制更新全局配置
            GlobalConfig.getInstance().setCurrentDistortionParams(params);
            GlobalConfig.getInstance().setCurrentConfigFileName(configInfo.displayName);

            // 更新显示
            updateCurrentConfigDisplay();

            // 打印系数验证信息
            List<Double> dxCoeffs = params.getDesignDx();
            List<Double> dyCoeffs = params.getDesignDy();
            Log.i(TAG, "loadSelectedConfig: Successfully loaded config '" + configInfo.displayName +
                  "' with " + dxCoeffs.size() + " DX and " + dyCoeffs.size() + " DY coefficients");

            if (dxCoeffs.size() >= 3 && dyCoeffs.size() >= 3) {
                Log.i(TAG, "loadSelectedConfig: First 3 DX coeffs: " + dxCoeffs.get(0) + ", " + dxCoeffs.get(1) + ", " + dxCoeffs.get(2));
                Log.i(TAG, "loadSelectedConfig: First 3 DY coeffs: " + dyCoeffs.get(0) + ", " + dyCoeffs.get(1) + ", " + dyCoeffs.get(2));
            }

            Toast.makeText(this, "已加载配置: " + configInfo.displayName, Toast.LENGTH_SHORT).show();
        } else {
            Log.e(TAG, "loadSelectedConfig: Failed to load config " + configInfo.displayName);
            Toast.makeText(this, "加载配置失败: " + configInfo.displayName, Toast.LENGTH_SHORT).show();
        }
    }




    private void updateCurrentConfigDisplay() {
        if (GlobalConfig.getInstance().getCurrentConfigFileName() != null) {
            binding.textViewCurrentConfigName.setText(GlobalConfig.getInstance().getCurrentConfigFileName());
        } else {
            binding.textViewCurrentConfigName.setText(R.string.no_config_loaded);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateCurrentConfigDisplay(); // Update in case it was changed in other activities

        // 刷新配置列表，以防用户在其他页面保存了新配置
        refreshConfigSpinner();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 处理屏幕旋转
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.i(TAG, "Screen rotated to landscape");
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Log.i(TAG, "Screen rotated to portrait");
        }

        // 重新设置布局以适应新的屏幕方向
        // Android会自动重新加载对应的布局文件
        Log.i(TAG, "Configuration changed, layout will be automatically updated");
    }

    /**
     * 刷新配置文件选择器，用于在用户保存新配置后更新列表
     */
    private void refreshConfigSpinner() {
        // 重新获取所有配置文件
        List<ConfigLoader.ConfigInfo> newConfigs = getAllAvailableConfigs();

        // 检查是否有新的配置文件
        if (newConfigs.size() != availableConfigs.size()) {
            Log.i(TAG, "refreshConfigSpinner: Config list changed, updating spinner");

            // 保存当前选择的配置名称
            String currentConfigName = GlobalConfig.getInstance().getCurrentConfigFileName();

            // 更新配置列表
            availableConfigs = newConfigs;

            // 更新显示名称数组
            String[] displayNames = new String[availableConfigs.size()];
            for (int i = 0; i < availableConfigs.size(); i++) {
                displayNames[i] = availableConfigs.get(i).displayName;
            }

            // 更新适配器
            configAdapter.clear();
            configAdapter.addAll(displayNames);
            configAdapter.notifyDataSetChanged();

            // 尝试恢复之前的选择
            if (currentConfigName != null) {
                for (int i = 0; i < availableConfigs.size(); i++) {
                    if (availableConfigs.get(i).displayName.equals(currentConfigName)) {
                        binding.spinnerConfigFiles.setSelection(i);
                        break;
                    }
                }
            }
        }
    }
}


