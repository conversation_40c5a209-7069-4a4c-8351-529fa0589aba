package com.candee.osttest;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.text.InputType;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.candee.osttest.databinding.ActivityMode2dBinding;
import com.candee.osttest.utils.ConfigLoader;
import com.candee.osttest.utils.Constants;
import com.candee.osttest.utils.DistortionParams;
import com.candee.osttest.utils.FileUtils;
import com.candee.osttest.utils.GlobalConfig;
import com.candee.osttest.utils.PresentationUtils;
import com.candee.osttest.utils.CoefficientEditHelper;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class Mode2DActivity extends AppCompatActivity {

    private static final String TAG = "Mode2DActivity";
    private ActivityMode2dBinding binding;
    private DistortionParams currentDistortionParams;
    private float currentFov = Constants.DEFAULT_FOV_DEGREES;
    private Uri selectedImageUri = null;
    private Bitmap selectedImageBitmap = null;
    private ActivityResultLauncher<String> getContentLauncher;

    // 21项式系数编辑框
    private EditText[] dxCoeffEditTexts = new EditText[21];
    private EditText[] dyCoeffEditTexts = new EditText[21];

    // 配置选择相关
    private List<ConfigLoader.ConfigInfo> availableConfigs;
    private ArrayAdapter<String> configAdapter;
    private boolean isInitializingSpinner = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMode2dBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setTitle(R.string.title_activity_mode2d);

        // 强制从GlobalConfig重新加载配置
        forceReloadConfiguration();

        // Set up UI components
        setupConfigSpinner();
        setupDistortionCoefficientsUI();
        setupFovSlider();
        setupImageSelection();
        setupControlButtons();
    }

    /**
     * 强制重新加载配置，确保获取最新的配置
     */
    private void forceReloadConfiguration() {
        Log.i(TAG, "forceReloadConfiguration: Force reloading configuration from GlobalConfig");

        currentDistortionParams = GlobalConfig.getInstance().getCurrentDistortionParams();
        currentFov = GlobalConfig.getInstance().getGlobalFov();

        String configName = GlobalConfig.getInstance().getCurrentConfigFileName();
        Log.i(TAG, "forceReloadConfiguration: Loaded config '" + configName + "'");

        if (currentDistortionParams != null) {
            List<Double> dxCoeffs = currentDistortionParams.getDesignDx();
            List<Double> dyCoeffs = currentDistortionParams.getDesignDy();
            Log.i(TAG, "forceReloadConfiguration: Config has " + dxCoeffs.size() + " DX and " + dyCoeffs.size() + " DY coefficients");

            if (dxCoeffs.size() >= 3 && dyCoeffs.size() >= 3) {
                Log.i(TAG, "forceReloadConfiguration: First 3 DX coeffs: " + dxCoeffs.get(0) + ", " + dxCoeffs.get(1) + ", " + dxCoeffs.get(2));
                Log.i(TAG, "forceReloadConfiguration: First 3 DY coeffs: " + dyCoeffs.get(0) + ", " + dyCoeffs.get(1) + ", " + dyCoeffs.get(2));
            }
        } else {
            Log.w(TAG, "forceReloadConfiguration: currentDistortionParams is null!");
        }
    }

    /**
     * 设置配置文件选择下拉列表
     */
    private void setupConfigSpinner() {
        Log.i(TAG, "setupConfigSpinner: Setting up configuration spinner");

        isInitializingSpinner = true;

        // 获取所有可用的配置文件（包括预设和用户保存的）
        availableConfigs = getAllAvailableConfigs();

        // 创建显示名称列表
        String[] displayNames = new String[availableConfigs.size()];
        for (int i = 0; i < availableConfigs.size(); i++) {
            displayNames[i] = availableConfigs.get(i).displayName;
        }

        // 设置适配器
        configAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, displayNames);
        configAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerConfigFiles.setAdapter(configAdapter);

        // 设置选择监听器
        binding.spinnerConfigFiles.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                // 防止初始化时覆盖配置
                if (isInitializingSpinner) {
                    Log.i(TAG, "onItemSelected: Skipping during initialization");
                    return;
                }

                if (position >= 0 && position < availableConfigs.size()) {
                    ConfigLoader.ConfigInfo selectedConfig = availableConfigs.get(position);
                    Log.i(TAG, "onItemSelected: User selected config " + selectedConfig.displayName);
                    loadSelectedConfig(selectedConfig);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Do nothing
            }
        });

        // 尝试选择当前配置（不触发加载）
        String currentConfigName = GlobalConfig.getInstance().getCurrentConfigFileName();
        Log.i(TAG, "setupConfigSpinner: Current config from GlobalConfig: " + currentConfigName);

        if (currentConfigName != null) {
            for (int i = 0; i < availableConfigs.size(); i++) {
                if (availableConfigs.get(i).displayName.equals(currentConfigName)) {
                    binding.spinnerConfigFiles.setSelection(i);
                    Log.i(TAG, "setupConfigSpinner: Selected current config '" + currentConfigName + "' at position " + i);
                    break;
                }
            }
        }

        // 完成初始化
        isInitializingSpinner = false;

        Log.i(TAG, "setupConfigSpinner: Spinner setup completed with " + availableConfigs.size() + " configs");
    }

    /**
     * 获取所有可用的配置文件（包括预设和用户保存的）
     */
    private List<ConfigLoader.ConfigInfo> getAllAvailableConfigs() {
        List<ConfigLoader.ConfigInfo> allConfigs = new ArrayList<>();

        // 添加预设配置文件
        allConfigs.addAll(ConfigLoader.getAvailableConfigs());

        // 添加用户保存的配置文件
        String[] savedFiles = FileUtils.getSavedConfigFiles(this);
        if (savedFiles != null) {
            for (String savedFile : savedFiles) {
                // 为用户保存的文件创建ConfigInfo，使用特殊标记表示这是用户文件
                allConfigs.add(new ConfigLoader.ConfigInfo(
                    "USER:" + savedFile,  // 使用特殊前缀标记用户文件
                    savedFile + " (用户保存)",
                    "用户保存的配置文件"
                ));
            }
        }

        return allConfigs;
    }

    /**
     * 加载选择的配置文件
     */
    private void loadSelectedConfig(ConfigLoader.ConfigInfo configInfo) {
        Log.i(TAG, "loadSelectedConfig: Loading config " + configInfo.displayName + " (fileName: " + configInfo.fileName + ")");

        DistortionParams params = null;

        // 检查是否是用户保存的配置文件
        if (configInfo.fileName.startsWith("USER:")) {
            // 用户保存的配置文件
            String actualFileName = configInfo.fileName.substring(5); // 移除"USER:"前缀
            Log.i(TAG, "loadSelectedConfig: Loading user saved config: " + actualFileName);
            params = FileUtils.loadDistortionParams(this, actualFileName);
        } else {
            // 预设配置文件
            Log.i(TAG, "loadSelectedConfig: Loading preset config from assets: " + configInfo.fileName);
            params = ConfigLoader.loadConfigFromAssets(this, configInfo.fileName);
        }

        if (params != null) {
            // 强制更新全局配置和当前配置
            GlobalConfig.getInstance().setCurrentDistortionParams(params);
            GlobalConfig.getInstance().setCurrentConfigFileName(configInfo.displayName);
            currentDistortionParams = params;

            // 更新UI显示
            updateCurrentConfigDisplay();

            // 重新加载系数到编辑框
            if (dxCoeffEditTexts != null && dyCoeffEditTexts != null) {
                loadCoefficientsToUI();
            }

            // 打印系数验证信息
            List<Double> dxCoeffs = params.getDesignDx();
            List<Double> dyCoeffs = params.getDesignDy();
            Log.i(TAG, "loadSelectedConfig: Successfully loaded config '" + configInfo.displayName +
                  "' with " + dxCoeffs.size() + " DX and " + dyCoeffs.size() + " DY coefficients");

            if (dxCoeffs.size() >= 3 && dyCoeffs.size() >= 3) {
                Log.i(TAG, "loadSelectedConfig: First 3 DX coeffs: " + dxCoeffs.get(0) + ", " + dxCoeffs.get(1) + ", " + dxCoeffs.get(2));
                Log.i(TAG, "loadSelectedConfig: First 3 DY coeffs: " + dyCoeffs.get(0) + ", " + dyCoeffs.get(1) + ", " + dyCoeffs.get(2));
            }

            // 如果副屏正在显示，立即应用新的畸变系数
            if (PresentationUtils.isPresentationShowing()) {
                ArPresentation presentation = PresentationUtils.getCurrentPresentation();
                if (presentation != null) {
                    presentation.setDistortionCoefficients(currentDistortionParams);
                    Log.d(TAG, "loadSelectedConfig: Real-time updated distortion coefficients to presentation");
                }
            }

            Toast.makeText(this, "已加载配置: " + configInfo.displayName, Toast.LENGTH_SHORT).show();
        } else {
            Log.e(TAG, "loadSelectedConfig: Failed to load config " + configInfo.displayName);
            Toast.makeText(this, "加载配置失败: " + configInfo.displayName, Toast.LENGTH_SHORT).show();
        }
    }

    private void setupDistortionCoefficientsUI() {
        Log.i(TAG, "setupDistortionCoefficientsUI: Setting up coefficient editing UI");

        // 显示当前配置名称
        updateCurrentConfigDisplay();

        // 创建21项式系数编辑框
        createCoefficientEditTexts();

        // 加载当前系数到编辑框（现在编辑框已创建）
        loadCoefficientsToUI();

        // 设置按钮事件
        binding.buttonSaveCoeffs.setOnClickListener(v -> saveCurrentConfig());
        binding.buttonSaveAsCoeffs.setOnClickListener(v -> showSaveAsDialog());
        binding.buttonResetCoeffs.setOnClickListener(v -> showResetConfirmationDialog());

        Log.i(TAG, "setupDistortionCoefficientsUI: UI setup completed");
    }

    private void updateCurrentConfigDisplay() {
        String configName = GlobalConfig.getInstance().getCurrentConfigFileName();
        if (configName != null) {
            binding.textViewCurrentConfig.setText("当前配置: " + configName);
        } else {
            binding.textViewCurrentConfig.setText("当前配置: 未知");
        }
    }

    private void createCoefficientEditTexts() {
        // 使用辅助类创建系数编辑框
        dxCoeffEditTexts = CoefficientEditHelper.createCoefficientEditTexts(this, binding.gridLayoutDxCoeffs, "DX");
        dyCoeffEditTexts = CoefficientEditHelper.createCoefficientEditTexts(this, binding.gridLayoutDyCoeffs, "DY");
    }

    private void loadCoefficientsToUI() {
        if (currentDistortionParams == null) {
            Log.w(TAG, "loadCoefficientsToUI: currentDistortionParams is null");
            return;
        }

        if (dxCoeffEditTexts == null || dyCoeffEditTexts == null) {
            Log.w(TAG, "loadCoefficientsToUI: EditText arrays not initialized yet");
            return;
        }

        List<Double> dxCoeffs = currentDistortionParams.getDesignDx();
        List<Double> dyCoeffs = currentDistortionParams.getDesignDy();

        Log.i(TAG, "loadCoefficientsToUI: Loading " + dxCoeffs.size() + " DX and " + dyCoeffs.size() + " DY coefficients");

        CoefficientEditHelper.loadCoefficientsToEditTexts(dxCoeffEditTexts, dxCoeffs);
        CoefficientEditHelper.loadCoefficientsToEditTexts(dyCoeffEditTexts, dyCoeffs);

        Log.i(TAG, "loadCoefficientsToUI: Coefficients loaded successfully");
    }

    private void saveCoefficientsFromUI() {
        if (currentDistortionParams == null) return;

        try {
            List<Double> dxCoeffs = CoefficientEditHelper.getCoefficientsFromEditTexts(dxCoeffEditTexts);
            List<Double> dyCoeffs = CoefficientEditHelper.getCoefficientsFromEditTexts(dyCoeffEditTexts);

            currentDistortionParams.setDesignDx(dxCoeffs);
            currentDistortionParams.setDesignDy(dyCoeffs);

            // 更新全局配置
            GlobalConfig.getInstance().setCurrentDistortionParams(currentDistortionParams);
            Log.d(TAG, "Saved coefficients to GlobalConfig");

            // 如果副屏正在显示，实时更新畸变系数
            if (PresentationUtils.isPresentationShowing()) {
                ArPresentation presentation = PresentationUtils.getCurrentPresentation();
                if (presentation != null) {
                    presentation.setDistortionCoefficients(currentDistortionParams);
                    Log.d(TAG, "Real-time updated distortion coefficients to presentation");
                }
            }

        } catch (NumberFormatException e) {
            Toast.makeText(this, "系数格式错误，请检查输入", Toast.LENGTH_SHORT).show();
            throw e;
        }
    }

    private void saveCurrentConfig() {
        try {
            saveCoefficientsFromUI();
            Toast.makeText(this, "系数已保存到当前配置", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error saving coefficients", e);
        }
    }

    private void showSaveAsDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("另存为新配置");

        EditText input = new EditText(this);
        input.setHint("输入配置文件名");
        builder.setView(input);

        builder.setPositiveButton("保存", (dialog, which) -> {
            String fileName = input.getText().toString().trim();
            if (fileName.isEmpty()) {
                Toast.makeText(this, "文件名不能为空", Toast.LENGTH_SHORT).show();
                return;
            }

            try {
                saveCoefficientsFromUI();
                boolean success = FileUtils.saveDistortionParams(this, currentDistortionParams, fileName);
                if (success) {
                    GlobalConfig.getInstance().setCurrentConfigFileName(fileName);
                    updateCurrentConfigDisplay();
                    Toast.makeText(this, "配置已保存为: " + fileName, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "保存失败", Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error saving as new config", e);
            }
        });

        builder.setNegativeButton("取消", (dialog, which) -> dialog.dismiss());
        builder.show();
    }

    private void setupFovSlider() {
        binding.seekBarFov.setMax((int)((Constants.MAX_FOV_DEGREES - Constants.MIN_FOV_DEGREES) * 10));
        binding.seekBarFov.setProgress((int)((currentFov - Constants.MIN_FOV_DEGREES) * 10));
        updateFovLabel();

        binding.seekBarFov.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                currentFov = Constants.MIN_FOV_DEGREES + (progress / 10.0f);
                updateFovLabel();

                if (fromUser) {
                    // 立即保存到全局配置
                    GlobalConfig.getInstance().setGlobalFov(currentFov);
                    Log.d(TAG, "FOV changed to " + currentFov + " degrees, saved to GlobalConfig");

                    // 如果副屏正在显示，实时更新
                    if (PresentationUtils.isPresentationShowing()) {
                        ArPresentation presentation = PresentationUtils.getCurrentPresentation();
                        if (presentation != null) {
                            presentation.setFov(currentFov);
                            Log.d(TAG, "Real-time updated FOV to presentation: " + currentFov + " degrees");
                        }
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // FOV已经在onProgressChanged中保存，这里只需要记录
                Log.d(TAG, "FOV adjustment completed: " + currentFov + " degrees");
            }
        });
    }

    private void updateFovLabel() {
        binding.textViewFovValue.setText(getString(R.string.fov_label, currentFov));
    }

    private void setupImageSelection() {
        getContentLauncher = registerForActivityResult(
            new ActivityResultContracts.GetContent(),
            uri -> {
                if (uri != null) {
                    selectedImageUri = uri;
                    loadSelectedImage();
                    updateSelectedImageLabel();
                }
            }
        );

        binding.buttonSelectImage.setOnClickListener(v -> {
            getContentLauncher.launch("image/*");
        });
    }

    private void loadSelectedImage() {
        if (selectedImageUri == null) return;

        try {
            InputStream inputStream = getContentResolver().openInputStream(selectedImageUri);
            selectedImageBitmap = BitmapFactory.decodeStream(inputStream);
            if (inputStream != null) {
                inputStream.close();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading image", e);
            Toast.makeText(this, "Error loading image: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void updateSelectedImageLabel() {
        if (selectedImageUri != null) {
            String fileName = selectedImageUri.getLastPathSegment();
            binding.textViewSelectedImage.setText(fileName);
        } else {
            binding.textViewSelectedImage.setText(R.string.no_image_selected);
        }
    }

    private void setupControlButtons() {
        binding.buttonApplyToSecondary.setOnClickListener(v -> {
            if (selectedImageBitmap == null) {
                Toast.makeText(this, "Please select an image first", Toast.LENGTH_SHORT).show();
                return;
            }

            Display secondaryDisplay = PresentationUtils.getSecondaryDisplay(this);
            if (secondaryDisplay == null) {
                Toast.makeText(this, "No secondary display found", Toast.LENGTH_SHORT).show();
                return;
            }

            // 先保存当前UI中的系数到全局配置
            try {
                saveCoefficientsFromUI();
                Log.d(TAG, "Saved coefficients from UI before applying to secondary display");
            } catch (Exception e) {
                Log.e(TAG, "Failed to save coefficients from UI", e);
                Toast.makeText(this, "系数格式错误，请检查输入", Toast.LENGTH_SHORT).show();
                return;
            }

            // 强制从GlobalConfig获取最新的参数值
            currentFov = GlobalConfig.getInstance().getGlobalFov();
            Log.d(TAG, "Retrieved latest FOV from GlobalConfig: " + currentFov);

            // Show presentation on secondary display (PresentationUtils会自动应用GlobalConfig参数)
            PresentationUtils.showPresentation(this, secondaryDisplay);

            // Configure presentation
            ArPresentation presentation = PresentationUtils.getCurrentPresentation();
            if (presentation != null) {
                Log.d(TAG, "Configuring presentation for 2D mode");

                // Set render mode (其他参数已在PresentationUtils中应用)
                presentation.setRenderMode(Constants.RENDER_MODE_2D);
                Log.d(TAG, "Set render mode to 2D");

                // 打印反畸变系数信息
                Log.d(TAG, "Current distortion params: " +
                      (currentDistortionParams != null ?
                       "Valid (description: " + currentDistortionParams.getDescription() + ")" :
                       "NULL"));

                if (currentDistortionParams != null) {
                    float[] dxCoeffs = currentDistortionParams.getDesignDxAsFloatArray();
                    float[] dyCoeffs = currentDistortionParams.getDesignDyAsFloatArray();

                    Log.d(TAG, "DX coeffs length: " + dxCoeffs.length +
                          ", First 3 values: " + dxCoeffs[0] + ", " + dxCoeffs[1] + ", " + dxCoeffs[2]);
                    Log.d(TAG, "DY coeffs length: " + dyCoeffs.length +
                          ", First 3 values: " + dyCoeffs[0] + ", " + dyCoeffs[1] + ", " + dyCoeffs[2]);

                    // 打印更多系数用于验证
                    StringBuilder dxLog = new StringBuilder("All DX coeffs: ");
                    StringBuilder dyLog = new StringBuilder("All DY coeffs: ");
                    for (int i = 0; i < Math.min(10, dxCoeffs.length); i++) {
                        dxLog.append(String.format("%.6e ", dxCoeffs[i]));
                        dyLog.append(String.format("%.6e ", dyCoeffs[i]));
                    }
                    Log.d(TAG, dxLog.toString());
                    Log.d(TAG, dyLog.toString());
                }

                // 注意：畸变系数、FOV、宽高比等参数已在PresentationUtils.showPresentation中自动应用
                Log.d(TAG, "Parameters automatically applied by PresentationUtils from GlobalConfig");

                // Update image
                if (selectedImageBitmap != null) {
                    Log.d(TAG, "Updating image: " + selectedImageBitmap.getWidth() + "x" + selectedImageBitmap.getHeight());
                    presentation.updateImage(selectedImageBitmap);
                } else {
                    Log.e(TAG, "Selected image bitmap is null!");
                }

                Log.d(TAG, "Presentation configuration completed");
                Toast.makeText(this, "已应用到副屏，请检查预畸变效果", Toast.LENGTH_SHORT).show();
            } else {
                Log.e(TAG, "Presentation is null!");
            }
        });

        binding.buttonStopSecondary.setOnClickListener(v -> {
            PresentationUtils.hidePresentation();
        });

        // 添加保存反畸变图像的按钮
        binding.buttonSaveDistortedImage.setOnClickListener(v -> {
            if (!PresentationUtils.isPresentationShowing()) {
                Toast.makeText(this, R.string.please_apply_to_secondary_first, Toast.LENGTH_SHORT).show();
                return;
            }

            // 检查存储权限
            if (!checkStoragePermissions()) {
                requestStoragePermissions();
                return;
            }

            // 保存反畸变图像
            saveDistortedImage();
        });
    }

    private boolean checkStoragePermissions() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            return Environment.isExternalStorageManager();
        } else {
            int write = ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            return write == PackageManager.PERMISSION_GRANTED;
        }
    }

    private void requestStoragePermissions() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse(String.format("package:%s", getApplicationContext().getPackageName())));
                startActivityForResult(intent, 2000);
            } catch (Exception e) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivityForResult(intent, 2000);
            }
        } else {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},
                    1000);
        }
    }

    private void saveDistortedImage() {
        ArPresentation presentation = PresentationUtils.getCurrentPresentation();
        if (presentation == null) {
            Toast.makeText(this, "Presentation not available", Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建保存目录
        File picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
        File appDir = new File(picturesDir, "ARGlassesDistorted");
        if (!appDir.exists() && !appDir.mkdirs()) {
            Toast.makeText(this, "Failed to create directory", Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建文件名
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String fileName = "DISTORTED_" + timeStamp + ".png";
        File outputFile = new File(appDir, fileName);

        // 保存图像
        if (presentation.captureFramebufferToFile(outputFile.getAbsolutePath())) {
            Toast.makeText(this, getString(R.string.save_distorted_image_success, outputFile.getAbsolutePath()), Toast.LENGTH_LONG).show();

            // 通知媒体扫描器更新图库
            MediaScannerConnection.scanFile(this,
                    new String[]{outputFile.getAbsolutePath()}, null,
                    (path, uri) -> Log.i(TAG, "Media scan completed: " + path));
        } else {
            Toast.makeText(this, R.string.save_distorted_image_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void showSaveDistortionDialog() {
        // Show dialog to save current distortion parameters
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.save_distortion_dialog_title);

        // Inflate custom layout with EditText for filename
        // For simplicity, we're not showing the actual dialog layout here

        builder.setPositiveButton(R.string.save_button, (dialog, which) -> {
            // Get filename from EditText
            String fileName = "example_config"; // This would come from the EditText

            if (fileName.isEmpty()) {
                Toast.makeText(this, R.string.error_filename_empty, Toast.LENGTH_SHORT).show();
                return;
            }

            // Save parameters
            boolean success = FileUtils.saveDistortionParams(this, currentDistortionParams, fileName);
            if (success) {
                Toast.makeText(this, getString(R.string.success_saving_file, fileName), Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, getString(R.string.error_saving_file, fileName), Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton(R.string.cancel_button, (dialog, which) -> dialog.dismiss());
        builder.show();
    }

    private void showLoadDistortionDialog() {
        String[] savedFiles = FileUtils.getSavedConfigFiles(this);
        if (savedFiles == null || savedFiles.length == 0) {
            Toast.makeText(this, "No saved configuration files found", Toast.LENGTH_SHORT).show();
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Select a configuration file");
        builder.setItems(savedFiles, (dialog, which) -> {
            String selectedFile = savedFiles[which];
            DistortionParams params = FileUtils.loadDistortionParams(this, selectedFile);
            if (params != null) {
                currentDistortionParams = params;
                GlobalConfig.getInstance().setCurrentDistortionParams(params);
                GlobalConfig.getInstance().setCurrentConfigFileName(selectedFile);
                Toast.makeText(this, getString(R.string.success_loading_file, selectedFile), Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, getString(R.string.error_loading_file, selectedFile), Toast.LENGTH_SHORT).show();
            }
        });
        builder.setNegativeButton(R.string.cancel_button, (dialog, which) -> dialog.dismiss());
        builder.show();
    }

    private void showResetConfirmationDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("确认重置")
               .setMessage("确定要重置系数为默认值吗？")
               .setPositiveButton("是", (dialog, which) -> {
                   currentDistortionParams = DistortionParams.getDefault();
                   GlobalConfig.getInstance().setCurrentDistortionParams(currentDistortionParams);
                   GlobalConfig.getInstance().setCurrentConfigFileName("默认内置系数");
                   updateCurrentConfigDisplay();
                   loadCoefficientsToUI();
                   Toast.makeText(this, "系数已重置为默认值", Toast.LENGTH_SHORT).show();
               })
               .setNegativeButton("否", (dialog, which) -> dialog.dismiss())
               .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.i(TAG, "onResume: Activity resumed");

        // 强制重新加载配置
        forceReloadConfiguration();

        // 更新UI显示
        updateCurrentConfigDisplay();

        // 重新加载系数到UI
        if (dxCoeffEditTexts != null && dyCoeffEditTexts != null) {
            loadCoefficientsToUI();
        }

        // 更新FOV滑块
        binding.seekBarFov.setProgress((int)((currentFov - Constants.MIN_FOV_DEGREES) * 10));
        updateFovLabel();

        // 更新配置选择器以反映当前配置
        updateConfigSpinnerSelection();
    }

    /**
     * 更新配置选择器的选择状态
     */
    private void updateConfigSpinnerSelection() {
        String currentConfigName = GlobalConfig.getInstance().getCurrentConfigFileName();
        if (currentConfigName != null && availableConfigs != null) {
            for (int i = 0; i < availableConfigs.size(); i++) {
                if (availableConfigs.get(i).displayName.equals(currentConfigName)) {
                    if (binding.spinnerConfigFiles.getSelectedItemPosition() != i) {
                        binding.spinnerConfigFiles.setSelection(i);
                        Log.i(TAG, "updateConfigSpinnerSelection: Updated spinner selection to position " + i);
                    }
                    break;
                }
            }
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 处理主屏旋转（副屏保持不变）
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.i(TAG, "Main screen rotated to landscape (secondary display unaffected)");
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Log.i(TAG, "Main screen rotated to portrait (secondary display unaffected)");
        }

        // 保存当前状态（包括副屏状态）
        saveCurrentState();

        // 检查副屏是否正在显示
        boolean wasSecondaryDisplayShowing = PresentationUtils.isPresentationShowing();
        ArPresentation currentPresentation = PresentationUtils.getCurrentPresentation();

        Log.d(TAG, "Before main screen rotation - Secondary display showing: " + wasSecondaryDisplayShowing);

        // 重新设置主屏布局
        binding = ActivityMode2dBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 重新初始化UI组件
        setupConfigSpinner();
        setupDistortionCoefficientsUI();
        setupFovSlider();
        setupImageSelection();
        setupControlButtons();

        // 恢复状态
        restoreCurrentState();

        // 验证副屏状态：确保副屏不受主屏旋转影响
        if (wasSecondaryDisplayShowing) {
            ArPresentation afterRotationPresentation = PresentationUtils.getCurrentPresentation();
            if (afterRotationPresentation != null && afterRotationPresentation.equals(currentPresentation)) {
                Log.d(TAG, "SUCCESS: Secondary display maintained after main screen rotation");
            } else {
                Log.w(TAG, "WARNING: Secondary display state changed during main screen rotation");
                // 如果副屏状态异常，尝试恢复
                if (afterRotationPresentation == null && selectedImageBitmap != null) {
                    Log.d(TAG, "Attempting to restore secondary display");
                    // 重新应用到副屏
                    Display secondaryDisplay = PresentationUtils.getSecondaryDisplay(this);
                    if (secondaryDisplay != null) {
                        PresentationUtils.showPresentation(this, secondaryDisplay);
                        ArPresentation newPresentation = PresentationUtils.getCurrentPresentation();
                        if (newPresentation != null) {
                            newPresentation.setRenderMode(Constants.RENDER_MODE_2D);
                            newPresentation.updateImage(selectedImageBitmap);
                            Log.d(TAG, "Secondary display restored successfully");
                        }
                    }
                }
            }
        }

        Log.i(TAG, "Main screen configuration changed, UI recreated and state restored (secondary display unaffected)");
    }

    private void saveCurrentState() {
        // 保存当前参数到GlobalConfig
        try {
            if (currentDistortionParams != null) {
                saveCoefficientsFromUI();
            }
            GlobalConfig.getInstance().setGlobalFov(currentFov);
        } catch (Exception e) {
            Log.w(TAG, "Failed to save current state", e);
        }
    }

    private void restoreCurrentState() {
        // 从GlobalConfig恢复状态
        forceReloadConfiguration();
        updateCurrentConfigDisplay();

        if (dxCoeffEditTexts != null && dyCoeffEditTexts != null) {
            loadCoefficientsToUI();
        }

        // 恢复FOV滑块
        binding.seekBarFov.setProgress((int)((currentFov - Constants.MIN_FOV_DEGREES) * 10));
        updateFovLabel();

        // 恢复图片选择状态
        updateSelectedImageLabel();

        // 更新配置选择器
        updateConfigSpinnerSelection();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // Save current parameters to global config
        GlobalConfig.getInstance().setGlobalFov(currentFov);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up resources
        if (selectedImageBitmap != null && !selectedImageBitmap.isRecycled()) {
            selectedImageBitmap.recycle();
            selectedImageBitmap = null;
        }
    }
}
