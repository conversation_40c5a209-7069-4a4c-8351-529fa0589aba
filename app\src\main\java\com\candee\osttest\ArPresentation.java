package com.candee.osttest;

import android.app.Presentation;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.media.MediaScannerConnection;
import android.opengl.GLSurfaceView;
import android.os.Bundle;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import com.candee.osttest.databinding.PresentationDisplayBinding;
import com.candee.osttest.utils.DistortionParams;
import com.candee.osttest.utils.GlobalConfig;
import com.candee.osttest.utils.Constants;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

public class ArPresentation extends Presentation {
    private static final String TAG = "ArPresentation";

    private GLSurfaceView glSurfaceView;
    private ArPresentationRenderer arRenderer;
    private MyNativeRenderer nativeRenderer; // Instance for this presentation
    private int renderMode_ = com.candee.osttest.utils.Constants.RENDER_MODE_2D; // 默认为2D模式

    public ArPresentation(Context outerContext, Display display) {
        super(outerContext, display);
        // Ensure the context is activity context if needed for certain UI ops,
        // but for Presentation, outerContext (usually an Activity) is fine.
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Presentation for display: " + getDisplay().getName());

        // 固定副屏方向为横屏，不受主屏旋转影响
        if (getWindow() != null && getWindow().getWindowManager() != null) {
            // 设置副屏固定为横屏方向
            getWindow().getAttributes().screenOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
            Log.d(TAG, "onCreate: Fixed secondary display orientation to LANDSCAPE");
        }

        // Inflate the layout for the presentation
        PresentationDisplayBinding binding = PresentationDisplayBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        glSurfaceView = binding.glSurfaceViewPresentation;

        // Configure GLSurfaceView
        glSurfaceView.setEGLContextClientVersion(3); // Request OpenGL ES 3.0

        nativeRenderer = new MyNativeRenderer(); // Create a new native renderer instance for this presentation
        arRenderer = new ArPresentationRenderer(getContext(), nativeRenderer);
        glSurfaceView.setRenderer(arRenderer);
        glSurfaceView.setRenderMode(GLSurfaceView.RENDERMODE_WHEN_DIRTY); // Render on demand

        // Optional: Keep the screen on for the presentation display
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        // 自动应用GlobalConfig中的参数
        applyGlobalConfigParameters();
    }

    public void updateImage(android.graphics.Bitmap bitmap) {
        if (arRenderer != null) {
            Log.d(TAG, "updateImage: Passing bitmap to ArPresentationRenderer");
            arRenderer.setImageBitmap(bitmap);
            if (glSurfaceView != null) {
                glSurfaceView.requestRender();
            }
        } else {
            Log.e(TAG, "updateImage: arRenderer is null!");
        }
    }

    public void setRenderMode(int mode) {
        Log.d(TAG, "setRenderMode: " + (mode == com.candee.osttest.utils.Constants.RENDER_MODE_2D ? "2D" : "3D"));

        // 更新渲染模式字段
        renderMode_ = mode;

        if (arRenderer != null) {
            // 使用 ArPresentationRenderer 中的方法，它会存储参数并在适当的时候应用
            arRenderer.setRenderMode(mode);
            requestRender();
        } else {
            Log.e(TAG, "setRenderMode: arRenderer is null");
        }
    }

    public void setDistortionCoefficients(DistortionParams params) {
        if (params != null) {
            Log.d(TAG, "setDistortionCoefficients");

            float[] dxCoeffs = params.getDesignDxAsFloatArray();
            float[] dyCoeffs = params.getDesignDyAsFloatArray();

            // 打印前几个系数用于调试
            StringBuilder dxLog = new StringBuilder("DX Coeffs: ");
            StringBuilder dyLog = new StringBuilder("DY Coeffs: ");

            for (int i = 0; i < Math.min(5, dxCoeffs.length); i++) {
                dxLog.append(dxCoeffs[i]).append(", ");
                dyLog.append(dyCoeffs[i]).append(", ");
            }

            Log.d(TAG, dxLog.toString());
            Log.d(TAG, dyLog.toString());

            if (arRenderer != null) {
                // 使用 ArPresentationRenderer 中的方法，它会存储参数并在适当的时候应用
                arRenderer.setDistortionCoefficients(dxCoeffs, dyCoeffs);
                Log.d(TAG, "Distortion coefficients set to renderer");
                requestRender();
            } else {
                Log.e(TAG, "setDistortionCoefficients: arRenderer is null");
            }
        } else {
            Log.e(TAG, "setDistortionCoefficients: params is null");
        }
    }

    public void setFov(float fovDegrees) {
        Log.d(TAG, "setFov: " + fovDegrees);

        if (arRenderer != null) {
            // 使用 ArPresentationRenderer 中的方法，它会存储参数并在适当的时候应用
            arRenderer.setFov(fovDegrees);
            requestRender();
        } else {
            Log.e(TAG, "setFov: arRenderer is null");
        }
    }

    public void setIpd(float ipdMeters) {
        Log.d(TAG, "setIpd: " + ipdMeters);

        if (arRenderer != null) {
            // 使用 ArPresentationRenderer 中的方法，它会存储参数并在适当的时候应用
            arRenderer.setIpd(ipdMeters);
            requestRender();
        } else {
            Log.e(TAG, "setIpd: arRenderer is null");
        }
    }

    public void setOpticalFocalPlaneDistance(float distanceMeters) {
        Log.d(TAG, "setOpticalFocalPlaneDistance: " + distanceMeters);

        if (arRenderer != null) {
            // 使用 ArPresentationRenderer 中的方法，它会存储参数并在适当的时候应用
            arRenderer.setFocalPlaneDistance(distanceMeters);
            requestRender();
        } else {
            Log.e(TAG, "setOpticalFocalPlaneDistance: arRenderer is null");
        }
    }

    public void setTargetDisplayMetrics(int width, int height) {
        Log.d(TAG, "setTargetDisplayMetrics: " + width + "x" + height);

        if (arRenderer != null) {
            // 使用 ArPresentationRenderer 中的方法，它会存储参数并在适当的时候应用
            arRenderer.setTargetDisplayMetrics(width, height);
            requestRender();
        } else {
            Log.e(TAG, "setTargetDisplayMetrics: arRenderer is null");
        }
    }


    public void requestRender() {
        if (glSurfaceView != null) {
            glSurfaceView.requestRender();
        }
    }

    /**
     * 捕获当前帧缓冲区并保存为PNG图像
     * @param filePath 保存路径
     * @return 是否成功保存
     */
    public boolean captureFramebufferToFile(String filePath) {
        if (nativeRenderer == null || glSurfaceView == null) {
            Log.e(TAG, "captureFramebufferToFile: nativeRenderer or glSurfaceView is null");
            return false;
        }

        final int width = glSurfaceView.getWidth();
        final int height = glSurfaceView.getHeight();

        if (width <= 0 || height <= 0) {
            Log.e(TAG, "captureFramebufferToFile: Invalid dimensions: " + width + "x" + height);
            return false;
        }

        Log.d(TAG, "captureFramebufferToFile: Capturing framebuffer " + width + "x" + height + " to " + filePath);

        // 强制渲染一帧，确保帧缓冲区包含最新内容
        requestRender();

        // 等待一小段时间，确保渲染完成
        try {
            Thread.sleep(200); // 增加等待时间，确保渲染完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 再次请求渲染，确保帧缓冲区包含最新内容
        requestRender();

        // 创建直接缓冲区用于存储像素数据
        final ByteBuffer buffer = ByteBuffer.allocateDirect(width * height * 4); // RGBA
        buffer.order(ByteOrder.nativeOrder());

        // 在GL线程上执行捕获操作
        final boolean[] success = new boolean[1];
        final CountDownLatch latch = new CountDownLatch(1);

        glSurfaceView.queueEvent(() -> {
            try {
                // 捕获帧缓冲区
                Log.d(TAG, "captureFramebufferToFile: Capturing framebuffer on GL thread");
                success[0] = nativeRenderer.native_captureFramebuffer(width, height, buffer);
                Log.d(TAG, "captureFramebufferToFile: Capture result: " + success[0]);
            } catch (Exception e) {
                Log.e(TAG, "captureFramebufferToFile: Exception during capture", e);
                success[0] = false;
            } finally {
                latch.countDown();
            }
        });

        try {
            // 等待GL线程完成捕获，增加超时时间
            if (!latch.await(5, TimeUnit.SECONDS)) { // 增加等待时间到5秒
                Log.e(TAG, "captureFramebufferToFile: Timeout waiting for GL thread");
                return false;
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "captureFramebufferToFile: Interrupted while waiting for GL thread", e);
            Thread.currentThread().interrupt();
            return false;
        }

        if (!success[0]) {
            Log.e(TAG, "captureFramebufferToFile: Failed to capture framebuffer");
            return false;
        }

        // 将缓冲区转换为Bitmap
        buffer.rewind();
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        bitmap.copyPixelsFromBuffer(buffer);

        Log.d(TAG, "captureFramebufferToFile: Created bitmap from buffer: " + bitmap.getWidth() + "x" + bitmap.getHeight());

        // 检查位图是否为全黑
        boolean isAllBlack = true;
        int[] pixels = new int[100]; // 采样100个像素点
        bitmap.getPixels(pixels, 0, 10, width/2-5, height/2-5, 10, 10);
        for (int pixel : pixels) {
            if (pixel != Color.BLACK && pixel != 0) {
                isAllBlack = false;
                break;
            }
        }

        if (isAllBlack) {
            Log.w(TAG, "captureFramebufferToFile: Captured image appears to be all black, but continuing anyway");
        }

        // 保存为PNG文件
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
            Log.d(TAG, "captureFramebufferToFile: Successfully saved to " + filePath);

            // 通知媒体扫描器更新图库
            Context context = getContext();
            if (context != null) {
                MediaScannerConnection.scanFile(context,
                        new String[]{filePath}, null,
                        (path, uri) -> Log.i(TAG, "Media scan completed: " + path));
            }

            return true;
        } catch (IOException e) {
            Log.e(TAG, "captureFramebufferToFile: Failed to save bitmap to file", e);
            return false;
        } finally {
            bitmap.recycle();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log.d(TAG, "onStart: Presentation started");
        if (glSurfaceView != null) {
            glSurfaceView.onResume();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.d(TAG, "onStop: Presentation stopped");
        if (glSurfaceView != null) {
            glSurfaceView.onPause();
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        Log.d(TAG, "onDetachedFromWindow: Cleaning up renderer.");
        if (arRenderer != null) {
            arRenderer.cleanUp(); // Clean up bitmap references
        }
        if (nativeRenderer != null) {
            // Ensure native_onDestroy is called on the GL thread if it releases GL resources.
            // GLSurfaceView's onPause typically handles this if native_onDestroy is called from there.
            // For an explicit dismiss, we might need to queue it to the GLThread.
            // For simplicity here, we assume native_onDestroy can be called directly.
            // Consider using glSurfaceView.queueEvent(() -> nativeRenderer.native_onDestroy());
             nativeRenderer.native_onDestroy();
        }
    }

    public void release() {
        // Custom method to ensure resources are released, called when presentation is dismissed
        Log.d(TAG, "release: Explicitly called release for Presentation.");
        onDetachedFromWindow(); // Call existing cleanup logic
    }

    /**
     * 自动应用GlobalConfig中的参数
     * 在onCreate中调用，确保arRenderer已经创建
     */
    private void applyGlobalConfigParameters() {
        GlobalConfig globalConfig = GlobalConfig.getInstance();
        Log.d(TAG, "Applying GlobalConfig parameters automatically");

        // 应用畸变参数
        if (globalConfig.getCurrentDistortionParams() != null) {
            setDistortionCoefficients(globalConfig.getCurrentDistortionParams());
            Log.d(TAG, "Auto-applied distortion coefficients from GlobalConfig");
        }

        // 应用FOV参数
        float fov = globalConfig.getGlobalFov();
        setFov(fov);
        Log.d(TAG, "Auto-applied FOV from GlobalConfig: " + fov);

        // 应用IPD参数
        float ipd = globalConfig.getGlobalIpd();
        setIpd(ipd);
        Log.d(TAG, "Auto-applied IPD from GlobalConfig: " + ipd);

        // 应用焦平面距离参数
        float focalDistance = globalConfig.getGlobalFocalDistance();
        setOpticalFocalPlaneDistance(focalDistance);
        Log.d(TAG, "Auto-applied focal distance from GlobalConfig: " + focalDistance);

        // 应用宽高比参数
        int aspectRatioId = globalConfig.getGlobalAspectRatioId();
        if (aspectRatioId == Constants.ASPECT_RATIO_16_9_ID) {
            setTargetDisplayMetrics(Constants.TARGET_DISPLAY_WIDTH_16_9, Constants.TARGET_DISPLAY_HEIGHT_16_9);
            Log.d(TAG, "Auto-applied 16:9 aspect ratio from GlobalConfig");
        } else {
            setTargetDisplayMetrics(Constants.TARGET_DISPLAY_WIDTH_32_9, Constants.TARGET_DISPLAY_HEIGHT_32_9);
            Log.d(TAG, "Auto-applied 32:9 aspect ratio from GlobalConfig");
        }

        Log.i(TAG, "GlobalConfig parameters auto-applied successfully");
    }
}
