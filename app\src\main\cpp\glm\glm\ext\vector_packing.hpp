/// @ref ext_vector_packing
/// @file glm/ext/vector_packing.hpp
///
/// @see core (dependence)
///
/// @defgroup ext_vector_packing GLM_EXT_vector_packing
/// @ingroup ext
///
/// Include <glm/ext/vector_packing.hpp> to use the features of this extension.
///
/// This extension provides a set of function to convert vectors to packed
/// formats.

#pragma once

// Dependency:
#include "../detail/qualifier.hpp"

#if GLM_MESSAGES == GLM_ENABLE && !defined(GLM_EXT_INCLUDED)
#	pragma message("GLM: GLM_EXT_vector_packing extension included")
#endif

namespace glm
{
	/// @addtogroup ext_vector_packing
	/// @{


	/// @}
}// namespace glm

#include "vector_packing.inl"
