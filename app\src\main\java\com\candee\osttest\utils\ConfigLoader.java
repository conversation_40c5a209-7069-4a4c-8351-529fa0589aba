package com.candee.osttest.utils;

import android.content.Context;
import android.util.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 配置文件加载器，用于加载 assets 中的预设配置文件
 */
public class ConfigLoader {
    private static final String TAG = "ConfigLoader";

    public static class ConfigInfo {
        public String fileName;
        public String displayName;
        public String description;

        public ConfigInfo(String fileName, String displayName, String description) {
            this.fileName = fileName;
            this.displayName = displayName;
            this.description = description;
        }
    }

    /**
     * 获取所有可用的预设配置文件
     */
    public static List<ConfigInfo> getAvailableConfigs() {
        List<ConfigInfo> configs = new ArrayList<>();
        configs.add(new ConfigInfo("distortion_design.json", "设计文件版本", "基于设计文件的反畸变系数"));
        configs.add(new ConfigInfo("distortion_real.json", "实际模组版本", "基于实际模组测量的制造畸变系数"));
        return configs;
    }

    /**
     * 从 assets 加载配置文件
     */
    public static DistortionParams loadConfigFromAssets(Context context, String fileName) {
        try {
            InputStream inputStream = context.getAssets().open(fileName);
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();

            String jsonString = new String(buffer, StandardCharsets.UTF_8);
            return parseJsonConfig(jsonString);

        } catch (IOException e) {
            Log.e(TAG, "Error loading config file: " + fileName, e);
            return null;
        }
    }

    /**
     * 解析 JSON 配置文件
     */
    private static DistortionParams parseJsonConfig(String jsonString) {
        try {
            JSONObject jsonObject = new JSONObject(jsonString);

            // 解析基本信息
            String name = jsonObject.optString("name", "未知配置");
            String description = jsonObject.optString("description", "");

            // 解析分辨率
            JSONObject resolution = jsonObject.optJSONObject("resolution");
            int width = resolution != null ? resolution.optInt("width", 1920) : 1920;
            int height = resolution != null ? resolution.optInt("height", 1200) : 1200;

            // 解析光学中心
            JSONObject opticalCenter = jsonObject.optJSONObject("optical_center");
            float cx = opticalCenter != null ? (float) opticalCenter.optDouble("cx", width / 2.0) : width / 2.0f;
            float cy = opticalCenter != null ? (float) opticalCenter.optDouble("cy", height / 2.0) : height / 2.0f;

            // 解析畸变系数
            JSONObject coefficients = jsonObject.getJSONObject("distortion_coefficients");
            JSONArray dxArray = coefficients.getJSONArray("dx");
            JSONArray dyArray = coefficients.getJSONArray("dy");

            if (dxArray.length() != 21 || dyArray.length() != 21) {
                Log.e(TAG, "Invalid coefficient array length. Expected 21, got dx:" +
                      dxArray.length() + ", dy:" + dyArray.length());
                return null;
            }

            List<Double> dxCoeffs = new ArrayList<>();
            List<Double> dyCoeffs = new ArrayList<>();

            for (int i = 0; i < 21; i++) {
                dxCoeffs.add(dxArray.getDouble(i));
                dyCoeffs.add(dyArray.getDouble(i));
            }

            // 创建 DistortionParams 对象
            DistortionParams params = new DistortionParams(name, dxCoeffs, dyCoeffs);

            Log.i(TAG, "Successfully loaded config: " + name);
            Log.i(TAG, "Resolution: " + width + "x" + height);
            Log.i(TAG, "Optical center: (" + cx + ", " + cy + ")");
            Log.i(TAG, "First few DX coeffs: " + dxCoeffs.get(0) + ", " + dxCoeffs.get(1) + ", " + dxCoeffs.get(2));
            Log.i(TAG, "First few DY coeffs: " + dyCoeffs.get(0) + ", " + dyCoeffs.get(1) + ", " + dyCoeffs.get(2));

            return params;

        } catch (JSONException e) {
            Log.e(TAG, "Error parsing JSON config", e);
            return null;
        }
    }

    /**
     * 获取默认配置（设计文件版本）
     */
    public static DistortionParams getDefaultConfig(Context context) {
        return loadConfigFromAssets(context, "distortion_design.json");
    }
}
