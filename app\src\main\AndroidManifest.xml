<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.candee.osttest">

    <!-- 请求OpenGL ES 版本 -->
    <uses-feature android:glEsVersion="0x00030000" android:required="true" />

    <!-- 读写外部存储权限 (如果系数文件或图片在外置存储) -->
    <!-- For Android 10 (API 29) and above, direct access to external storage is restricted.
         Consider using Scoped Storage (MediaStore API or Storage Access Framework)
         For simplicity in this demo, we'll use legacy storage for older versions
         and rely on internal app-specific storage or SAF for newer ones. -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- 用于保存反畸变后的图像 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
                     tools:ignore="ScopedStorage" />

    <!-- Required for Presentation API to detect secondary displays -->
    <uses-permission android:name="android.permission.ACCESS_DISPLAY_MANAGER" />


    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.OstTest"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="33">

        <activity android:name=".MainActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".Mode2DActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified" />
        <activity android:name=".Mode3DActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified" />

    </application>

</manifest>
