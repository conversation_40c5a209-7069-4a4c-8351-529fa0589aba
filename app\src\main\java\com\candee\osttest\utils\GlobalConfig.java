package com.candee.osttest.utils;

import android.content.Context;
import android.util.Log;
import com.candee.osttest.utils.DistortionParams;

/**
 * 全局配置类，用于在不同 Activity 之间共享配置
 */
public class GlobalConfig {
    private static GlobalConfig instance;

    // 单例模式
    public static synchronized GlobalConfig getInstance() {
        if (instance == null) {
            instance = new GlobalConfig();
        }
        return instance;
    }

    // 当前畸变参数
    private DistortionParams currentDistortionParams;

    // 当前配置文件名
    private String currentConfigFileName = "默认内置系数";

    // 全局 FOV 设置
    private float globalFov = Constants.DEFAULT_FOV_DEGREES;

    // 全局 IPD 设置
    private float globalIpd = Constants.DEFAULT_IPD_METERS;

    // 全局焦平面距离设置
    private float globalFocalDistance = Constants.DEFAULT_FOCAL_DISTANCE_METERS;

    // 全局宽高比设置
    private int globalAspectRatioId = Constants.ASPECT_RATIO_16_9_ID;

    private GlobalConfig() {
        // 初始化默认参数
        currentDistortionParams = DistortionParams.getDefault();
    }

    public void initializeWithDefaultConfig(Context context) {
        // 尝试加载默认的设计文件版本配置
        DistortionParams defaultParams = ConfigLoader.getDefaultConfig(context);
        if (defaultParams != null) {
            currentDistortionParams = defaultParams;
            currentConfigFileName = "设计文件版本";
            Log.i("GlobalConfig", "Initialized with default config from assets");
        } else {
            Log.w("GlobalConfig", "Failed to load default config, using built-in defaults");
        }
    }

    public DistortionParams getCurrentDistortionParams() {
        return currentDistortionParams;
    }

    public void setCurrentDistortionParams(DistortionParams params) {
        this.currentDistortionParams = params;
    }

    public String getCurrentConfigFileName() {
        return currentConfigFileName;
    }

    public void setCurrentConfigFileName(String fileName) {
        this.currentConfigFileName = fileName;
    }

    public float getGlobalFov() {
        return globalFov;
    }

    public void setGlobalFov(float fov) {
        this.globalFov = fov;
    }

    public int getGlobalAspectRatioId() {
        return globalAspectRatioId;
    }

    public void setGlobalAspectRatioId(int aspectRatioId) {
        this.globalAspectRatioId = aspectRatioId;
    }

    public float getGlobalIpd() {
        return globalIpd;
    }

    public void setGlobalIpd(float ipd) {
        this.globalIpd = ipd;
    }

    public float getGlobalFocalDistance() {
        return globalFocalDistance;
    }

    public void setGlobalFocalDistance(float focalDistance) {
        this.globalFocalDistance = focalDistance;
    }
}
