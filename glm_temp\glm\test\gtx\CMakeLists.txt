glmCreateTestGTC(gtx)
glmCreateTestGTC(gtx_associated_min_max)
glmCreateTestGTC(gtx_closest_point)
glmCreateTestGTC(gtx_color_encoding)
glmCreateTestGTC(gtx_color_space_YCoCg)
glmCreateTestGTC(gtx_color_space)
glmCreateTestGTC(gtx_common)
glmCreateTestGTC(gtx_compatibility)
glmCreateTestGTC(gtx_component_wise)
glmCreateTestGTC(gtx_easing)
glmCreateTestGTC(gtx_euler_angle)
glmCreateTestGTC(gtx_extend)
glmCreateTestGTC(gtx_extended_min_max)
glmCreateTestGTC(gtx_exterior_product)
glmCreateTestGTC(gtx_fast_exponential)
glmCreateTestGTC(gtx_fast_square_root)
glmCreateTestGTC(gtx_fast_trigonometry)
glmCreateTestGTC(gtx_functions)
glmCreateTestGTC(gtx_gradient_paint)
glmCreateTestGTC(gtx_handed_coordinate_space)
glmCreateTestGTC(gtx_integer)
glmCreateTestGTC(gtx_intersect)
glmCreateTestGTC(gtx_io)
glmCreateTestGTC(gtx_load)
glmCreateTestGTC(gtx_log_base)
glmCreateTestGTC(gtx_matrix_cross_product)
glmCreateTestGTC(gtx_matrix_decompose)
glmCreateTestGTC(gtx_matrix_factorisation)
glmCreateTestGTC(gtx_matrix_interpolation)
glmCreateTestGTC(gtx_matrix_major_storage)
glmCreateTestGTC(gtx_matrix_operation)
glmCreateTestGTC(gtx_matrix_query)
glmCreateTestGTC(gtx_matrix_transform_2d)
glmCreateTestGTC(gtx_norm)
glmCreateTestGTC(gtx_normal)
glmCreateTestGTC(gtx_normalize_dot)
glmCreateTestGTC(gtx_number_precision)
glmCreateTestGTC(gtx_orthonormalize)
glmCreateTestGTC(gtx_optimum_pow)
glmCreateTestGTC(gtx_perpendicular)
glmCreateTestGTC(gtx_polar_coordinates)
glmCreateTestGTC(gtx_projection)
glmCreateTestGTC(gtx_quaternion)
glmCreateTestGTC(gtx_dual_quaternion)
glmCreateTestGTC(gtx_range)
glmCreateTestGTC(gtx_rotate_normalized_axis)
glmCreateTestGTC(gtx_rotate_vector)
glmCreateTestGTC(gtx_scalar_multiplication)
glmCreateTestGTC(gtx_scalar_relational)
glmCreateTestGTC(gtx_spline)
glmCreateTestGTC(gtx_string_cast)
glmCreateTestGTC(gtx_texture)
glmCreateTestGTC(gtx_type_aligned)
glmCreateTestGTC(gtx_type_trait)
glmCreateTestGTC(gtx_vec_swizzle)
glmCreateTestGTC(gtx_vector_angle)
glmCreateTestGTC(gtx_vector_query)
glmCreateTestGTC(gtx_wrap)
