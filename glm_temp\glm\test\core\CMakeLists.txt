glmCreateTestGTC(core_cpp_constexpr)
glmCreateTestGTC(core_cpp_defaulted_ctor)
glmCreateTestGTC(core_force_aligned_gentypes)
glmCreateTestGTC(core_force_ctor_init)
glmCreateTestGTC(core_force_cxx03)
glmCreateTestGTC(core_force_cxx98)
glmCreateTestGTC(core_force_arch_unknown)
glmCreateTestGTC(core_force_compiler_unknown)
glmCreateTestGTC(core_force_cxx_unknown)
glmCreateTestGTC(core_force_explicit_ctor)
glmCreateTestGTC(core_force_inline)
glmCreateTestGTC(core_force_platform_unknown)
glmCreateTestGTC(core_force_pure)
glmCreateTestGTC(core_force_unrestricted_gentype)
glmCreateTestGTC(core_force_xyzw_only)
glmCreateTestGTC(core_force_quat_wxyz)
glmCreateTestGTC(core_type_aligned)
glmCreateTestGTC(core_type_cast)
glmCreateTestGTC(core_type_ctor)
glmCreateTestGTC(core_type_int)
glmCreateTestGTC(core_type_length)
glmCreateTestGTC(core_type_mat2x2)
glmCreateTestGTC(core_type_mat2x3)
glmCreateTestGTC(core_type_mat2x4)
glmCreateTestGTC(core_type_mat3x2)
glmCreateTestGTC(core_type_mat3x3)
glmCreateTestGTC(core_type_mat3x4)
glmCreateTestGTC(core_type_mat4x2)
glmCreateTestGTC(core_type_mat4x3)
glmCreateTestGTC(core_type_mat4x4)
glmCreateTestGTC(core_type_vec1)
glmCreateTestGTC(core_type_vec2)
glmCreateTestGTC(core_type_vec3)
glmCreateTestGTC(core_type_vec4)
glmCreateTestGTC(core_func_common)
glmCreateTestGTC(core_func_exponential)
glmCreateTestGTC(core_func_geometric)
glmCreateTestGTC(core_func_integer)
glmCreateTestGTC(core_func_integer_bit_count)
glmCreateTestGTC(core_func_integer_find_lsb)
glmCreateTestGTC(core_func_integer_find_msb)
glmCreateTestGTC(core_func_matrix)
glmCreateTestGTC(core_func_noise)
glmCreateTestGTC(core_func_packing)
glmCreateTestGTC(core_func_trigonometric)
glmCreateTestGTC(core_func_vector_relational)
glmCreateTestGTC(core_func_swizzle)
glmCreateTestGTC(core_setup_force_cxx98)
glmCreateTestGTC(core_setup_force_size_t_length)
glmCreateTestGTC(core_setup_message)
glmCreateTestGTC(core_setup_platform_unknown)
glmCreateTestGTC(core_setup_precision)
