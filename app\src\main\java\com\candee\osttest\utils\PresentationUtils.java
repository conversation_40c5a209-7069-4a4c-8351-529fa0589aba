package com.candee.osttest.utils;

import android.app.Activity;
import android.content.Context;
import android.hardware.display.DisplayManager;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.graphics.Point;

import com.candee.osttest.ArPresentation;

public class PresentationUtils {
    private static final String TAG = "PresentationUtils";
    private static ArPresentation currentPresentation;

    public static Display getSecondaryDisplay(Context context) {
        DisplayManager displayManager = (DisplayManager) context.getSystemService(Context.DISPLAY_SERVICE);
        if (displayManager == null) {
            Log.e(TAG, "DisplayManager not available.");
            return null;
        }

        Display[] displays = displayManager.getDisplays();
        if (displays.length > 1) {
            // Heuristic: The first display (displays[0]) is usually the primary device screen.
            // Any subsequent display could be the secondary one.
            // For more robust detection, you might need to check display flags or names if available.
            // For DP connected glasses, it's often displays[1].
            Log.i(TAG, "Found " + displays.length + " displays. Using display 1 as secondary.");
            return displays[1]; // Assuming the second display is the AR glasses
        }
        Log.w(TAG, "No secondary display found (displays.length <= 1)");
        return null;
    }

    public static void showPresentation(Activity activity, Display secondaryDisplay) {
        if (secondaryDisplay == null) {
            Log.e(TAG, "Cannot show presentation, secondary display is null.");
            return;
        }

        // 安全地关闭现有的Presentation
        if (currentPresentation != null) {
            try {
                if (currentPresentation.isShowing()) {
                    Log.w(TAG, "Presentation is already showing. Dismissing the old one.");
                    currentPresentation.dismiss();
                }
                currentPresentation.release();
            } catch (Exception e) {
                Log.w(TAG, "Error dismissing old presentation", e);
            }
            currentPresentation = null;
        }

        Log.i(TAG, "Creating ArPresentation on display: " + secondaryDisplay.getName());

        try {
            // 创建新的Presentation
            currentPresentation = new ArPresentation(activity, secondaryDisplay);

            // 显示Presentation
            currentPresentation.show();
            Log.i(TAG, "ArPresentation shown successfully with auto-applied GlobalConfig parameters");

        } catch (Exception e) {
            Log.e(TAG, "Error creating or showing presentation", e);
            // 清理失败的Presentation
            if (currentPresentation != null) {
                try {
                    currentPresentation.release();
                } catch (Exception cleanupException) {
                    Log.w(TAG, "Error during cleanup", cleanupException);
                }
                currentPresentation = null;
            }
        }
    }

    public static void hidePresentation() {
        if (currentPresentation != null) {
            try {
                if (currentPresentation.isShowing()) {
                    Log.i(TAG, "Dismissing ArPresentation.");
                    currentPresentation.dismiss();
                }
                currentPresentation.release();
            } catch (Exception e) {
                Log.w(TAG, "Error hiding presentation", e);
            }
            currentPresentation = null;
        }
    }

    public static ArPresentation getCurrentPresentation() {
        return currentPresentation;
    }

    public static boolean isPresentationShowing() {
        return currentPresentation != null && currentPresentation.isShowing();
    }


}
